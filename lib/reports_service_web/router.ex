defmodule ReportsServiceWeb.Router do
  use ReportsServiceWeb, :router

  alias ExFirebaseAuthPlug.Plug.VerifyHeader
  alias ExIkarus.Plug.EnableExperimental
  alias ReportsServiceWeb.Plugs.SellerContext

  pipeline :api do
    plug :accepts, ["json"]
  end

  pipeline :api_doc do
    plug OpenApiSpex.Plug.PutApiSpec, module: ReportsServiceWeb.ApiSpec
  end

  pipeline :user_authentication do
    plug :accepts, ["json"]

    if Mix.env() != :test do
      plug VerifyHeader
    end
  end

  pipeline :ensure_user_authentication do
    plug :accepts, ["json"]

    if Mix.env() != :test do
      plug VerifyHeader
      plug ExFirebaseAuthPlug.Plug.EnsureAuthenticated
      plug SellerContext
    end
  end

  pipeline :enable_experimental do
    plug EnableExperimental
  end

  pipeline :snake_case do
    plug ProperCase.Plug.SnakeCaseParams
  end

  scope "/" do
    get "/", ReportsServiceWeb.HealthController, :welcome
  end

  scope "/reports" do
    scope "/api" do
      get "/swaggerui", OpenApiSpex.Plug.SwaggerUI, path: "/reports/api/openapi"
    end

    scope "/api" do
      pipe_through [:api, :api_doc]
      get "/openapi", OpenApiSpex.Plug.RenderSpec, []
    end
  end

  scope "/reports", ReportsServiceWeb do
    pipe_through [:enable_experimental]

    scope "/api" do
      pipe_through [:api]

      # HealthController
      get "/health", HealthController, :show
    end

    scope "/api" do
      pipe_through [:ensure_user_authentication]

      # DashboardController
      get "/dashboards/events/:id", DashboardController, :show_event_dashboard
      get "/dashboards/events/:id/tickets", DashboardController, :show_ticket_dashboard

      # Organizer Controller
      get "/organizers/:id/events", OrganizerController, :show_events
      get "/organizers/:id/imported-events", OrganizerController, :show_imported_events

      # Ticket Controller
      get "/tickets", TicketController, :show_tickets
      get "/tickets/count", TicketController, :count_tickets

      # Order Controller
      get "/orders", OrderController, :index

      # Event Controller
      get "/events/:id/demographics", EventController, :show_demographics
      get "/events/:id/city", EventController, :show_city

      scope "/" do
        pipe_through [:snake_case]
        # EntranceArea Controller
        get "/entrance-areas", EntranceAreaController, :index
      end

      # ImportedEvents Controller
      post "/imported_events", ImportedEventController, :create
    end

    scope "/api" do
      pipe_through [:ensure_user_authentication]

      get "/dashboards/organizer", DashboardController, :show_organizer_dashboard
    end
  end
end
